package corp.jamaro.jamaroescritoriofx.appfx.service;


import corp.jamaro.jamaroescritoriofx.appfx.model.Cliente;
import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.UUID;

/**
 * Servicio para gestionar las operaciones relacionadas con los clientes.
 * Proporciona funcionalidades de búsqueda y suscripción a actualizaciones de clientes.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ClienteService {

    private final ConnectionService connectionService;

    // Rutas para las operaciones del servidor
    private static final String ROUTE_SUBSCRIBE = "cliente.subscribe";
    private static final String ROUTE_SEARCH_BY_NAME = "cliente.searchByName";
    private static final String ROUTE_SEARCH_BY_DOCUMENT = "cliente.searchByDocument";

    /**
     * Se suscribe a las actualizaciones de un Cliente específico.
     *
     * @param clienteId ID del Cliente al que se quiere suscribir
     * @return Flux que emite el Cliente actual y sus actualizaciones futuras
     */
    public Flux<Cliente> subscribeToClienteUpdates(UUID clienteId) {
        log.debug("Suscribiendo a actualizaciones del Cliente con ID: {}", clienteId);
        return connectionService.authenticatedSubscription(ROUTE_SUBSCRIBE, clienteId, Cliente.class);
    }

    /**
     * Busca clientes usando expresión regular en nombre, apellido o razónSocial.
     * Búsqueda insensible a mayúsculas y minúsculas, sin importar posición.
     * Limitado a 30 resultados para optimizar rendimiento.
     *
     * @param searchTerm Término de búsqueda para nombre, apellido o razónSocial
     * @return Flux de hasta 30 clientes que coinciden con la búsqueda
     */
    public Flux<Cliente> searchClientesByName(String searchTerm) {
        log.debug("Buscando clientes por nombre/apellido/razónSocial con término: {}", searchTerm);
        var request = new SearchClientesByNameRequest(searchTerm);
        return connectionService.authenticatedSubscription(ROUTE_SEARCH_BY_NAME, request, Cliente.class);
    }

    /**
     * Busca clientes por coincidencia exacta en dni, ruc o otroDocumento.
     * Búsqueda insensible a mayúsculas y minúsculas.
     *
     * @param document Documento a buscar (dni, ruc o otroDocumento)
     * @return Flux de clientes que coinciden exactamente con el documento
     */
    public Flux<Cliente> searchClientesByDocument(String document) {
        log.debug("Buscando clientes por documento exacto: {}", document);
        var request = new SearchClientesByDocumentRequest(document);
        return connectionService.authenticatedSubscription(ROUTE_SEARCH_BY_DOCUMENT, request, Cliente.class);
    }

    // Records para los parámetros de las solicitudes

    /**
     * Datos para la solicitud de búsqueda de clientes por nombre/apellido/razónSocial.
     *
     * @param searchTerm Término de búsqueda para nombre, apellido o razónSocial
     */
    public record SearchClientesByNameRequest(String searchTerm) {}

    /**
     * Datos para la solicitud de búsqueda de clientes por documento.
     *
     * @param document Documento a buscar (dni, ruc o otroDocumento)
     */
    public record SearchClientesByDocumentRequest(String document) {}
}
