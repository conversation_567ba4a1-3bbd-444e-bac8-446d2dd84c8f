package corp.jamaro.jamaroescritoriofx.appfx.util;

import javafx.application.Platform;
import javafx.scene.control.*;
import javafx.scene.layout.GridPane;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Optional;

/**
 * Servicio para mostrar distintos tipos de Alert (errores, info, confirmaciones)
 * con un estilo unificado (CSS) y sin apenas traducción de mensajes.
 */
@Service
@Slf4j
public class AlertUtil {

    // Ajusta la ruta a tu stylesheet real
    private static final String STYLESHEET_PATH = "/css/styles.css";

    // =========================================================================
    //                         CONFIRMACIÓN (SÍ/NO)
    // =========================================================================

    public Optional<ButtonType> showConfirmation(
            String title,
            String header,
            String content,
            String textBtnOk,
            String textBtnCancel
    ) {
        Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
        confirmDialog.setTitle(title);
        confirmDialog.setHeaderText(header);
        confirmDialog.setContentText(content);

        ButtonType btnOk = new ButtonType(textBtnOk, ButtonBar.ButtonData.OK_DONE);
        ButtonType btnCancel = new ButtonType(textBtnCancel, ButtonBar.ButtonData.CANCEL_CLOSE);
        confirmDialog.getButtonTypes().setAll(btnOk, btnCancel);

        applyStylesheet(confirmDialog);
        return confirmDialog.showAndWait();
    }

    // =========================================================================
    //                           ERRORES
    // =========================================================================

    /**
     * Muestra un error partiendo de una excepción, mostrando tal cual
     * el mensaje que el servidor (o la lógica interna) devuelva.
     */
    public void showError(Throwable error) {
        log.error("Error: {}", error.getMessage(), error);
        String msg = error.getMessage();
        if (msg == null || msg.isBlank()) {
            msg = "Ha ocurrido un error desconocido en el servidor.";
        }
        showErrorAlert(msg);
    }

    /**
     * Muestra un error con mensaje personalizado (sin stacktrace).
     */
    public void showError(String userFriendlyMessage) {
        log.error("Error informado al usuario: {}", userFriendlyMessage);
        if (userFriendlyMessage == null || userFriendlyMessage.isBlank()) {
            userFriendlyMessage = "Ha ocurrido un error. Intente nuevamente.";
        }
        showErrorAlert(userFriendlyMessage);
    }

    /**
     * Muestra un error con detalles (stacktrace) en un área expandible.
     */
    public void showErrorWithDetails(Throwable error, String contextMessage) {
        log.error("Error con detalles: {}", error.getMessage(), error);

        StringWriter sw = new StringWriter();
        error.printStackTrace(new PrintWriter(sw));
        String exceptionText = sw.toString();

        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Error");
            alert.setHeaderText(contextMessage != null ? contextMessage : "Error con detalles");

            TextArea textArea = new TextArea(exceptionText);
            textArea.setEditable(false);
            textArea.setWrapText(true);
            textArea.setMaxWidth(Double.MAX_VALUE);
            textArea.setMaxHeight(Double.MAX_VALUE);

            GridPane expandableContent = new GridPane();
            expandableContent.setMaxWidth(Double.MAX_VALUE);
            expandableContent.add(new Label("Detalles del error:"), 0, 0);
            expandableContent.add(textArea, 0, 1);

            alert.getDialogPane().setExpandableContent(expandableContent);

            applyStylesheet(alert);
            alert.showAndWait();
        });
    }

    /**
     * Muestra un AlertType.ERROR con el contenido dado.
     */
    private void showErrorAlert(String content) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Error");
            alert.setHeaderText("Ocurrió un problema");
            alert.setContentText(content);

            applyStylesheet(alert);
            alert.showAndWait();
        });
    }

    // =========================================================================
    //                         INFO / WARNING
    // =========================================================================

    public void showInfo(String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle("Información");
            alert.setHeaderText(null);
            alert.setContentText(message);

            applyStylesheet(alert);
            alert.showAndWait();
        });
    }

    public void showInfo(String title, String content) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);        // <-- Título en la ventana
            alert.setHeaderText(null);    // <-- Sin header
            alert.setContentText(content);

            applyStylesheet(alert);
            alert.showAndWait();
        });
    }

    public void showWarning(String message) {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.WARNING);
            alert.setTitle("Advertencia");
            alert.setHeaderText(null);
            alert.setContentText(message);

            applyStylesheet(alert);
            alert.showAndWait();
        });
    }

    // =========================================================================
    //                     APLICAR HOJA DE ESTILOS
    // =========================================================================

    private void applyStylesheet(Alert alert) {
        alert.getDialogPane().getStylesheets().add(
                getClass().getResource(STYLESHEET_PATH).toExternalForm()
        );
    }
}
