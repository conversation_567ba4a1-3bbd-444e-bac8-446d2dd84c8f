package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.model.Cliente;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Item;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Producto;
import corp.jamaro.jamaroescritoriofx.appfx.service.ClienteService;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.util.LoadingUtil;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.BienServicioCargado;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.BienServicioDevuelto;

import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.Sale;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.SaleService;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.scene.control.ButtonBar;
import javafx.scene.control.ButtonType;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyCodeCombination;
import javafx.scene.input.KeyCombination;
import javafx.scene.Parent;
import javafx.scene.control.*;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.util.Callback;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.AutoCompletionBinding;
import org.controlsfx.control.textfield.CustomTextField;
import org.controlsfx.control.textfield.TextFields;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;
import reactor.util.retry.Retry;

import java.net.URL;
import java.text.NumberFormat;
import java.time.Duration;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.UUID;
import java.util.stream.Collectors;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class SaleController extends BaseController {

    // Getters para los ListViews (necesarios para que SaleGuiController pueda acceder a ellos)
    public ListView<BienServicioCargado> getBienServicioCargados() {
        return bienServicioCargados;
    }

    public ListView<BienServicioDevuelto> getBienServicioDevueltos() {
        // Returning null since bienServicioDevueltos has been removed
        return null;
    }

    private final SaleService saleService;
    private final ClienteService clienteService;
    private final AlertUtil alertUtil;
    private final SpringFXMLLoader springFXMLLoader;

    private UUID saleId;
    private Sale currentSale;
    private Disposable saleSubscription;
    private NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();

    // Autocompletado para clientes
    private AutoCompletionBinding<Cliente> autoCompletionBinding;

    // Debounce para el botón limpiar (en milisegundos)
    private static final long LIMPIAR_DEBOUNCE_DELAY = 1000;
    private long lastLimpiarClickTime = 0;


    @FXML
    private AnchorPane anchorBotones;

    @FXML
    private AnchorPane anchorSale;

    @FXML
    private ListView<BienServicioCargado> bienServicioCargados;

    // bienServicioDevueltos has been removed from the FXML

    @FXML
    private Button btnCalcular;

    @FXML
    private Button btnImprimir;

    @FXML
    private Button btnLimpiar;

    @FXML
    private Button btnVender;

    @FXML
    private Label lblDescuento;

    @FXML
    private Label lblTotalMontoAcordado;

    @FXML
    private Label lblTotalMontoInicial;

    @FXML
    private Label lblTipoVenta;

    @FXML
    private ProgressIndicator loadingIndicator;

    // splitPaneBienesServicios has been removed from the FXML

    @FXML
    private StackPane stackPaneSale;

    @FXML
    private TextField txtCodigo;

    @FXML
    private CustomTextField txtDocumentoNombreRazon;

    @FXML
    private VBox vbTotalVenta;

    /**
     * Establece el ID de la venta y se suscribe a sus actualizaciones.
     * @param saleId ID de la venta
     */
    public void setSaleId(UUID saleId) {
        this.saleId = saleId;
        log.debug("SaleController: Configurando saleId={}", saleId);

        // Inicializar la UI
        initializeUI();

        // Suscribirse a las actualizaciones de la venta
        subscribeToSaleUpdates();
    }

    /**
     * Inicializa la interfaz de usuario.
     */
    private void initializeUI() {
        // Configurar el cell factory para el ListView de BienServicioCargado
        // Usar un enfoque con cache para evitar recargar el FXML constantemente
        bienServicioCargados.setCellFactory(new Callback<ListView<BienServicioCargado>, ListCell<BienServicioCargado>>() {
            @Override
            public ListCell<BienServicioCargado> call(ListView<BienServicioCargado> param) {
                return new ListCell<BienServicioCargado>() {
                    // Cache para las celdas, usando el ID de BienServicioCargado como clave
                    private final Map<String, Parent> cellCache = new HashMap<>();
                    private final Map<String, BienServicioCargadoController> controllerCache = new HashMap<>();

                    @Override
                    protected void updateItem(BienServicioCargado item, boolean empty) {
                        super.updateItem(item, empty);

                        if (empty || item == null) {
                            setText(null);
                            setGraphic(null);
                            // Deshabilitar eventos de mouse en celdas vacías
                            setDisable(true);
                            setMouseTransparent(true);
                        } else {
                            // Habilitar eventos de mouse en celdas con contenido
                            setDisable(false);
                            setMouseTransparent(false);

                            // Usar el ID del BienServicioCargado como clave para el cache
                            String bienServicioId = item.getId().toString();

                            try {
                                Parent cellContent;
                                BienServicioCargadoController controller;

                                // Verificar si ya existe en el cache
                                if (cellCache.containsKey(bienServicioId)) {
                                    cellContent = cellCache.get(bienServicioId);
                                    controller = controllerCache.get(bienServicioId);
                                } else {
                                    // Crear una nueva instancia y guardarla en el cache
                                    cellContent = springFXMLLoader.load("fxml/sale/bienServicioCargado.fxml");
                                    controller = springFXMLLoader.getController(cellContent);

                                    // Guardar en el cache
                                    cellCache.put(bienServicioId, cellContent);
                                    controllerCache.put(bienServicioId, controller);
                                }

                                // Configurar el controlador con el item actual
                                controller.setBienServicioCargado(item, saleId);
                                setGraphic(cellContent);
                            } catch (Exception e) {
                                log.error("Error al cargar bienServicioCargado.fxml: {}", e.getMessage(), e);
                                setText("Error al cargar la vista");
                            }
                        }
                    }
                };
            }
        });

        // Configurar el ListView para que se ajuste correctamente
        // Remover altura fija para permitir expansión de celdas
        bienServicioCargados.setFixedCellSize(-1); // Altura variable para permitir expansión

        // Configurar el comportamiento de scroll para evitar que los elementos expandidos se tapen
        bienServicioCargados.setOnScrollTo(event -> event.consume()); // Evitar scroll automático
        bienServicioCargados.setPrefWidth(javafx.scene.layout.Region.USE_COMPUTED_SIZE); // Usar el ancho calculado

        // Agregar detector de clic para evitar clics en celdas vacías
        bienServicioCargados.setOnMouseClicked(event -> {
            // Verificar si el clic ocurrió en una celda vacía
            int clickedIndex = bienServicioCargados.getSelectionModel().getSelectedIndex();
            if (clickedIndex >= bienServicioCargados.getItems().size() || clickedIndex < 0) {
                // Clic en celda vacía, ignorar
                log.debug("Clic detectado en celda vacía del ListView de BienServicioCargado, ignorando");
                event.consume();
            }
        });

        // Asegurar que las celdas se ajusten al ancho disponible
        bienServicioCargados.widthProperty().addListener((obs, oldVal, newVal) -> {
            // Forzar actualización de las celdas cuando cambia el ancho
            bienServicioCargados.refresh();
        });

        // Agregar listener para mantener la visibilidad de las celdas seleccionadas durante el movimiento del SplitPane
        bienServicioCargados.getSelectionModel().selectedItemProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null) {
                // Forzar actualización de la celda seleccionada para asegurar su visibilidad
                Platform.runLater(() -> bienServicioCargados.refresh());
            }
        });

        // BienServicioDevuelto ListView configuration has been removed

        // Configurar autocompletado para clientes
        configureClienteAutoComplete();

        // Configurar lblTipoVenta como clickeable
        configureTipoVentaClick();
    }

    /**
     * Se suscribe a las actualizaciones de la venta desde el servidor.
     */
    private void subscribeToSaleUpdates() {
        if (saleId == null) {
            log.warn("No se puede suscribir a actualizaciones: saleId es null");
            return;
        }

        // Cancelar suscripción anterior si existe
        if (saleSubscription != null && !saleSubscription.isDisposed()) {
            saleSubscription.dispose();
        }

        // Suscribirse a las actualizaciones de la venta
        saleSubscription = LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                saleService.subscribeToSaleUpdates(saleId)
                        .retryWhen(Retry.backoff(3, Duration.ofMillis(200))
                                .filter(err -> err.getMessage() != null
                                        && err.getMessage().toLowerCase().contains("deadlock"))
                                .doBeforeRetry(signal ->
                                        log.warn("Reintentando suscripción a Sale (intento #{}) por: {}",
                                                signal.totalRetries() + 1, signal.failure().getMessage()))
                        ),
                this::onSaleUpdate,
                this::handleSubscriptionError,
                () -> log.debug("Suscripción a Sale completada.")
        );
        registerSubscription(saleSubscription);
    }

    /**
     * Maneja las actualizaciones recibidas de la venta.
     * @param sale La venta actualizada
     */
    private void onSaleUpdate(Sale sale) {
        runOnUiThread(() -> {
            log.debug("Recibida actualización de Sale: {}", sale.getId());
            this.currentSale = sale;

            // Actualizar la UI con los datos de la venta
            updateUI(sale);
        });
    }

    /**
     * Actualiza la interfaz de usuario con los datos de la venta.
     * @param sale La venta con los datos actualizados
     */
    private void updateUI(Sale sale) {
        // Actualizar información del cliente
        if (sale.getCliente() != null) {
            String clienteInfo = "";

            // Obtener el documento principal (DNI, RUC u otro)
            if (sale.getCliente().getDni() != null && !sale.getCliente().getDni().isEmpty()) {
                clienteInfo = sale.getCliente().getDni();
            } else if (sale.getCliente().getRuc() != null && !sale.getCliente().getRuc().isEmpty()) {
                clienteInfo = sale.getCliente().getRuc();
            } else if (sale.getCliente().getOtroDocumento() != null && !sale.getCliente().getOtroDocumento().isEmpty()) {
                clienteInfo = sale.getCliente().getOtroDocumento();
            }

            // Agregar nombre o razón social
            String nombreCompleto = "";
            if (sale.getCliente().getRazonSocial() != null && !sale.getCliente().getRazonSocial().isEmpty()) {
                nombreCompleto = sale.getCliente().getRazonSocial();
            } else {
                // Construir nombre completo a partir de nombre y apellido
                if (sale.getCliente().getNombre() != null && !sale.getCliente().getNombre().isEmpty()) {
                    nombreCompleto = sale.getCliente().getNombre();
                    if (sale.getCliente().getApellido() != null && !sale.getCliente().getApellido().isEmpty()) {
                        nombreCompleto += " " + sale.getCliente().getApellido();
                    }
                }
            }

            // Combinar documento y nombre
            if (!clienteInfo.isEmpty() && !nombreCompleto.isEmpty()) {
                clienteInfo += " - " + nombreCompleto;
            } else if (!nombreCompleto.isEmpty()) {
                clienteInfo = nombreCompleto;
            }

            txtDocumentoNombreRazon.setText(clienteInfo);
        } else {
            txtDocumentoNombreRazon.setText("");
        }

        // Actualizar tipo de venta
        if (sale.getTipoVenta() != null) {
            lblTipoVenta.setText(sale.getTipoVenta().toString());
        } else {
            lblTipoVenta.setText("PROFORMA");
        }

        // Actualizar totales
        double totalInicial = 0.0;
        double totalAcordado = 0.0;

        if (sale.getBienServicioCargados() != null) {
            for (BienServicioCargado bsc : sale.getBienServicioCargados()) {
                if (bsc.getPrecioInicial() != null && bsc.getCantidad() != null) {
                    totalInicial += bsc.getPrecioInicial() * bsc.getCantidad();
                }

                // Usar montoAcordado si está disponible, sino calcular con precioAcordado o precioInicial
                if (bsc.getMontoAcordado() != null) {
                    totalAcordado += bsc.getMontoAcordado();
                } else if (bsc.getPrecioAcordado() != null && bsc.getCantidad() != null) {
                    totalAcordado += bsc.getPrecioAcordado() * bsc.getCantidad();
                } else if (bsc.getPrecioInicial() != null && bsc.getCantidad() != null) {
                    totalAcordado += bsc.getPrecioInicial() * bsc.getCantidad();
                }
            }
        }

        // Si hay totales en el objeto Sale, usarlos en lugar de los calculados
        if (sale.getTotalMontoInicial() != null) {
            totalInicial = sale.getTotalMontoInicial();
        }
        if (sale.getTotalMontoAcordado() != null) {
            totalAcordado = sale.getTotalMontoAcordado();
        }

        // Calcular descuento como la diferencia entre total inicial y total acordado
        double descuento = totalInicial - totalAcordado;

        lblTotalMontoInicial.setText(currencyFormat.format(totalInicial));
        lblDescuento.setText(currencyFormat.format(descuento));
        lblTotalMontoAcordado.setText(currencyFormat.format(totalAcordado));

        // Actualizar listas de items
        bienServicioCargados.getItems().clear();
        if (sale.getBienServicioCargados() != null) {
            // Ordenar los BienServicioCargado por createdAt de más antiguo a más reciente
            List<BienServicioCargado> sortedItems = sale.getBienServicioCargados().stream()
                    .sorted(Comparator.comparing(BienServicioCargado::getCreatedAt,
                            Comparator.nullsLast(Comparator.naturalOrder())))
                    .collect(Collectors.toList());

            log.debug("Ordenando {} BienServicioCargado por createdAt (más antiguo a más reciente)", sortedItems.size());
            bienServicioCargados.getItems().addAll(sortedItems);
        }

        // BienServicioDevueltos handling has been removed
    }

    /**
     * Maneja errores en la suscripción a la venta.
     * @param error El error ocurrido
     */
    private void handleSubscriptionError(Throwable error) {
        log.error("Error en la suscripción de Sale: {}", error.getMessage(), error);
        runOnUiThread(() ->
                alertUtil.showError("Error al recibir datos de la venta: " + error.getMessage())
        );
    }

    /**
     * Método agnostico para agregar un Item a la venta cuando se hace doble clic en él.
     * Este método será llamado desde SearchProductGuiController.
     *
     * @param item El Item a agregar
     * @param producto El Producto padre del Item
     */
    public void addItemToSale(Item item, Producto producto) {
        if (saleId == null) {
            log.warn("No se puede agregar item: saleId es null");
            alertUtil.showError("No hay una venta activa para agregar el item.");
            return;
        }

        if (item == null || item.getCodCompuesto() == null) {
            log.warn("No se puede agregar item: item o codCompuesto es null");
            alertUtil.showError("El item seleccionado no es válido.");
            return;
        }

        // Solicitar la cantidad al usuario
        TextInputDialog quantityDialog = new TextInputDialog("1.0");
        quantityDialog.setTitle("Cantidad");
        quantityDialog.setHeaderText("Ingrese la cantidad para el item: " +
                (item.getMarca() != null && item.getMarca().getNombre() != null ? item.getMarca().getNombre() + " - " : "") +
                item.getDescripcion());
        quantityDialog.setContentText("Cantidad:");

        Optional<String> quantityResult = quantityDialog.showAndWait();
        if (quantityResult.isPresent()) {
            try {
                double cantidad = Double.parseDouble(quantityResult.get());

                // Verificar que la cantidad sea positiva
                if (cantidad <= 0) {
                    alertUtil.showError("La cantidad debe ser mayor que cero.");
                    return;
                }

                // Solicitar el precio si es necesario (opcional)
                Double precioInicial = null;

                // Usar el precio de venta público como precio inicial por defecto
                if (item.getPrecioVentaPublico() != null) {
                    precioInicial = item.getPrecioVentaPublico();
                }

                // Enviar la solicitud al servidor
                loadingIndicator.setVisible(true);
                subscribeOnBoundedElastic(
                        saleService.addItemToSale(saleId, item.getCodCompuesto(), precioInicial, cantidad),
                        response -> {
                            loadingIndicator.setVisible(false);
                            if (response.success()) {
                                log.info("Item agregado exitosamente a la venta: {}", item.getCodCompuesto());
                                // No necesitamos actualizar la UI aquí, ya que recibiremos una actualización a través de la suscripción
                            } else {
                                log.warn("Error al agregar item a la venta: {}", response.message());
                                runOnUiThread(() -> alertUtil.showError("Error al agregar item: " + response.message()));
                            }
                        },
                        error -> {
                            loadingIndicator.setVisible(false);
                            log.error("Error al agregar item a la venta: {}", error.getMessage(), error);
                            runOnUiThread(() -> alertUtil.showError("Error al agregar item: " + error.getMessage()));
                        }
                );

            } catch (NumberFormatException e) {
                alertUtil.showError("Por favor, ingrese un número válido para la cantidad.");
            }
        }
    }

    /**
     * Maneja el evento del botón Limpiar para eliminar todos los BienServicioCargado.
     */
    @FXML
    private void handleBtnLimpiar() {
        debouncedLimpiar();
    }

    /**
     * Implementa debounce para el botón limpiar.
     */
    private void debouncedLimpiar() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastLimpiarClickTime < LIMPIAR_DEBOUNCE_DELAY) {
            log.debug("Limpiar ignorado por debounce");
            return;
        }
        lastLimpiarClickTime = currentTime;

        performLimpiar();
    }

    /**
     * Ejecuta la limpieza de todos los BienServicioCargado.
     */
    private void performLimpiar() {
        if (saleId == null) {
            log.warn("No se puede limpiar: saleId es null");
            alertUtil.showError("No hay una venta activa para limpiar.");
            return;
        }

        // Confirmar la acción con el usuario
        var result = alertUtil.showConfirmation(
                "Confirmar Limpieza",
                "¿Está seguro de que desea eliminar todos los items de la venta?",
                "Esta acción no se puede deshacer.",
                "Sí, eliminar",
                "Cancelar"
        );

        if (result.isEmpty() || result.get().getButtonData() != ButtonBar.ButtonData.OK_DONE) {
            return;
        }

        // Mostrar indicador de carga
        loadingIndicator.setVisible(true);

        // Llamar al servicio para eliminar todos los BienServicioCargado
        subscribeOnBoundedElastic(
                saleService.deleteAllBienServicioCargado(saleId),
                response -> {
                    loadingIndicator.setVisible(false);
                    if (response.success()) {
                        log.info("Todos los BienServicioCargado eliminados exitosamente de la venta: {}", saleId);
                        // No necesitamos actualizar la UI aquí, ya que recibiremos una actualización a través de la suscripción
                    } else {
                        log.warn("Error al eliminar todos los BienServicioCargado: {}", response.message());
                        runOnUiThread(() -> alertUtil.showError("Error al limpiar la venta: " + response.message()));
                    }
                },
                error -> {
                    loadingIndicator.setVisible(false);
                    log.error("Error al eliminar todos los BienServicioCargado: {}", error.getMessage(), error);
                    runOnUiThread(() -> alertUtil.showError("Error al limpiar la venta: " + error.getMessage()));
                }
        );
    }

    // SplitPane divider visibility management has been removed

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // Inicializar componentes básicos
        // La inicialización completa se hace en setSaleId() cuando se recibe el ID de la venta

        // Configurar atajo de teclado Ctrl+L para limpiar
        stackPaneSale.setOnKeyPressed(event -> {
            KeyCombination ctrlL = new KeyCodeCombination(KeyCode.L, KeyCombination.CONTROL_DOWN);
            if (ctrlL.match(event)) {
                debouncedLimpiar();
                event.consume();
            }
        });

        // Asegurar que el StackPane pueda recibir eventos de teclado
        stackPaneSale.setFocusTraversable(true);

        log.debug("Inicializando SaleController");

        // SplitPane and bienServicioDevueltos initialization has been removed
    }

    @Override
    public void onClose() {
        super.onClose();

        // Cancelar suscripción si existe
        if (saleSubscription != null && !saleSubscription.isDisposed()) {
            saleSubscription.dispose();
        }

        log.info("SaleController cerrado.");
    }

    /**
     * Configura el autocompletado para el campo de cliente.
     */
    private void configureClienteAutoComplete() {
        // Habilitar el campo para edición
        txtDocumentoNombreRazon.setDisable(false);
        txtDocumentoNombreRazon.setEditable(true);

        // Configurar autocompletado con búsqueda dinámica
        autoCompletionBinding = TextFields.bindAutoCompletion(
                txtDocumentoNombreRazon,
                request -> {
                    String searchTerm = request.getUserText();
                    if (searchTerm == null || searchTerm.trim().length() < 2) {
                        return java.util.Collections.emptyList();
                    }

                    try {
                        // Buscar por documento primero (búsqueda exacta)
                        if (searchTerm.matches("\\d+")) {
                            // Si es solo números, buscar por documento
                            return clienteService.searchClientesByDocument(searchTerm)
                                    .take(10) // Limitar a 10 resultados
                                    .collectList()
                                    .block(java.time.Duration.ofSeconds(2));
                        } else {
                            // Si contiene letras, buscar por nombre
                            return clienteService.searchClientesByName(searchTerm)
                                    .take(10) // Limitar a 10 resultados
                                    .collectList()
                                    .block(java.time.Duration.ofSeconds(2));
                        }
                    } catch (Exception e) {
                        log.warn("Error en búsqueda de clientes: {}", e.getMessage());
                        return java.util.Collections.emptyList();
                    }
                }
        );

        // Configurar cómo se muestra cada cliente en la lista
        autoCompletionBinding.setConverter(new javafx.util.StringConverter<Cliente>() {
            @Override
            public String toString(Cliente cliente) {
                if (cliente == null) return "";

                StringBuilder display = new StringBuilder();

                // Agregar documento
                if (cliente.getDni() != null && !cliente.getDni().isEmpty()) {
                    display.append(cliente.getDni());
                } else if (cliente.getRuc() != null && !cliente.getRuc().isEmpty()) {
                    display.append(cliente.getRuc());
                } else if (cliente.getOtroDocumento() != null && !cliente.getOtroDocumento().isEmpty()) {
                    display.append(cliente.getOtroDocumento());
                }

                // Agregar nombre o razón social
                String nombre = "";
                if (cliente.getRazonSocial() != null && !cliente.getRazonSocial().isEmpty()) {
                    nombre = cliente.getRazonSocial();
                } else if (cliente.getNombre() != null && !cliente.getNombre().isEmpty()) {
                    nombre = cliente.getNombre();
                    if (cliente.getApellido() != null && !cliente.getApellido().isEmpty()) {
                        nombre += " " + cliente.getApellido();
                    }
                }

                if (!nombre.isEmpty()) {
                    if (display.length() > 0) {
                        display.append(" - ");
                    }
                    display.append(nombre);
                }

                return display.toString();
            }

            @Override
            public Cliente fromString(String string) {
                // No necesitamos implementar esto para autocompletado
                return null;
            }
        });

        // Manejar la selección de un cliente
        autoCompletionBinding.setOnAutoCompleted(event -> {
            Cliente selectedCliente = event.getCompletion();
            if (selectedCliente != null && saleId != null) {
                log.debug("Cliente seleccionado: {}", selectedCliente.getId());

                // Actualizar el cliente en la venta
                loadingIndicator.setVisible(true);
                subscribeOnBoundedElastic(
                        saleService.updateCliente(saleId, selectedCliente.getId()),
                        response -> {
                            loadingIndicator.setVisible(false);
                            if (response.success()) {
                                log.info("Cliente actualizado exitosamente en la venta: {}", selectedCliente.getId());
                                // No necesitamos actualizar la UI aquí, ya que recibiremos una actualización a través de la suscripción
                            } else {
                                log.warn("Error al actualizar cliente: {}", response.message());
                                runOnUiThread(() -> alertUtil.showError("Error al actualizar cliente: " + response.message()));
                            }
                        },
                        error -> {
                            loadingIndicator.setVisible(false);
                            log.error("Error al actualizar cliente: {}", error.getMessage(), error);
                            runOnUiThread(() -> alertUtil.showError("Error al actualizar cliente: " + error.getMessage()));
                        }
                );
            }
        });

        // Configurar menú contextual para eliminar cliente
        ContextMenu clienteContextMenu = new ContextMenu();
        MenuItem removeClienteItem = new MenuItem("Eliminar Cliente");
        removeClienteItem.setOnAction(event -> removeCliente());
        clienteContextMenu.getItems().add(removeClienteItem);
        txtDocumentoNombreRazon.setContextMenu(clienteContextMenu);

        log.debug("Autocompletado de clientes configurado");
    }

    /**
     * Configura el lblTipoVenta para que sea clickeable y permita cambiar el tipo de venta.
     */
    private void configureTipoVentaClick() {
        lblTipoVenta.setOnMouseClicked(event -> {
            if (saleId == null) {
                log.warn("No se puede cambiar tipo de venta: saleId es null");
                return;
            }

            // Crear lista de opciones de tipo de venta
            ChoiceDialog<Sale.TipoVenta> dialog = new ChoiceDialog<>();
            dialog.setTitle("Cambiar Tipo de Venta");
            dialog.setHeaderText("Seleccione el nuevo tipo de venta:");
            dialog.setContentText("Tipo de venta:");

            // Agregar todas las opciones de TipoVenta
            dialog.getItems().addAll(Sale.TipoVenta.values());

            // Establecer el valor actual como seleccionado
            if (currentSale != null && currentSale.getTipoVenta() != null) {
                dialog.setSelectedItem(currentSale.getTipoVenta());
            } else {
                dialog.setSelectedItem(Sale.TipoVenta.PROFORMA);
            }

            Optional<Sale.TipoVenta> result = dialog.showAndWait();
            if (result.isPresent()) {
                Sale.TipoVenta newTipoVenta = result.get();

                // Solo actualizar si es diferente al actual
                if (currentSale == null || !newTipoVenta.equals(currentSale.getTipoVenta())) {
                    log.debug("Cambiando tipo de venta a: {}", newTipoVenta);

                    loadingIndicator.setVisible(true);
                    subscribeOnBoundedElastic(
                            saleService.updateTipoVenta(saleId, newTipoVenta),
                            response -> {
                                loadingIndicator.setVisible(false);
                                if (response.success()) {
                                    log.info("Tipo de venta actualizado exitosamente: {}", newTipoVenta);
                                    // No necesitamos actualizar la UI aquí, ya que recibiremos una actualización a través de la suscripción
                                } else {
                                    log.warn("Error al actualizar tipo de venta: {}", response.message());
                                    runOnUiThread(() -> alertUtil.showError("Error al actualizar tipo de venta: " + response.message()));
                                }
                            },
                            error -> {
                                loadingIndicator.setVisible(false);
                                log.error("Error al actualizar tipo de venta: {}", error.getMessage(), error);
                                runOnUiThread(() -> alertUtil.showError("Error al actualizar tipo de venta: " + error.getMessage()));
                            }
                    );
                }
            }
        });

        // Agregar estilo visual para indicar que es clickeable
        lblTipoVenta.setOnMouseEntered(event -> lblTipoVenta.setStyle("-fx-cursor: hand;"));
        lblTipoVenta.setOnMouseExited(event -> lblTipoVenta.setStyle(""));

        log.debug("lblTipoVenta configurado como clickeable");
    }

    /**
     * Elimina el cliente actual de la venta (convierte en venta genérica).
     */
    public void removeCliente() {
        if (saleId == null) {
            log.warn("No se puede eliminar cliente: saleId es null");
            return;
        }

        // Confirmar la acción
        Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
        confirmDialog.setTitle("Eliminar Cliente");
        confirmDialog.setHeaderText("¿Está seguro de que desea eliminar el cliente de esta venta?");
        confirmDialog.setContentText("La venta se convertirá en una venta genérica.");

        ButtonType buttonTypeYes = new ButtonType("Sí", ButtonBar.ButtonData.YES);
        ButtonType buttonTypeNo = new ButtonType("No", ButtonBar.ButtonData.NO);
        confirmDialog.getButtonTypes().setAll(buttonTypeYes, buttonTypeNo);

        Optional<ButtonType> result = confirmDialog.showAndWait();
        if (result.isPresent() && result.get() == buttonTypeYes) {
            log.debug("Eliminando cliente de la venta");

            loadingIndicator.setVisible(true);
            subscribeOnBoundedElastic(
                    saleService.updateCliente(saleId, null), // null para eliminar cliente
                    response -> {
                        loadingIndicator.setVisible(false);
                        if (response.success()) {
                            log.info("Cliente eliminado exitosamente de la venta");
                            // No necesitamos actualizar la UI aquí, ya que recibiremos una actualización a través de la suscripción
                        } else {
                            log.warn("Error al eliminar cliente: {}", response.message());
                            runOnUiThread(() -> alertUtil.showError("Error al eliminar cliente: " + response.message()));
                        }
                    },
                    error -> {
                        loadingIndicator.setVisible(false);
                        log.error("Error al eliminar cliente: {}", error.getMessage(), error);
                        runOnUiThread(() -> alertUtil.showError("Error al eliminar cliente: " + error.getMessage()));
                    }
            );
        }
    }
}
