package corp.jamaro.jamaroescritoriofx.appfx.ventas.service;

import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.Sale;
import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * Servicio para gestionar las operaciones relacionadas con las ventas (Sale).
 *
 * Este servicio se comunica con el servidor mediante RSocket para:
 * 1. Suscribirse a actualizaciones de un Sale
 * 2. Enviar solicitudes de actualización al servidor
 * 3. Recibir respuestas de operaciones mediante su subscription RSocket
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SaleService {

    private static final String ROUTE_SUBSCRIBE = "sale.subscribe";
    private static final String ROUTE_UPDATE_CLIENTE = "sale.updateCliente";
    private static final String ROUTE_ADD_ITEM = "sale.addItemToSale";
    private static final String ROUTE_UPDATE_BIEN_SERVICIO_CARGADO = "sale.updateBienServicioCargado";
    private static final String ROUTE_DELETE_BIEN_SERVICIO_CARGADO = "sale.deleteBienServicioCargado";
    private static final String ROUTE_DELETE_ALL_BIEN_SERVICIO_CARGADO = "sale.deleteAllBienServicioCargado";
    private static final String ROUTE_INICIAR_VENTA_CONTADO = "sale.iniciarVentaContado";
    private static final String ROUTE_INICIAR_BIEN_SERVICIO_DEVUELTO = "sale.iniciarBienServicioDevuelto";

    private final ConnectionService connectionService;

    /**
     * Permite a los clientes suscribirse a actualizaciones de un Sale específico.
     *
     * El flujo emitirá:
     * 1. El estado actual del Sale como primer elemento
     * 2. Todas las actualizaciones futuras que se realicen sobre ese Sale
     *
     * @param saleId ID del Sale al que se quiere suscribir
     * @return Flux que emite el Sale actual y sus actualizaciones futuras
     */
    public Flux<Sale> subscribeToSaleUpdates(UUID saleId) {
        log.debug("Subscribing to Sale updates for id: {}", saleId);
        return connectionService.authenticatedSubscription(ROUTE_SUBSCRIBE, saleId, Sale.class);
    }

    /**
     * Actualiza el Cliente de un Sale.
     *
     * @param saleId ID del Sale a actualizar
     * @param clienteId UUID del Cliente a asignar
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<OperationResponse> updateCliente(UUID saleId, UUID clienteId) {
        log.debug("Updating cliente for Sale with id: {}, clienteId: {}", saleId, clienteId);
        var request = new UpdateClienteRequest(saleId, clienteId);
        return connectionService.authenticatedRequest(ROUTE_UPDATE_CLIENTE, request, OperationResponse.class);
    }

    /**
     * Agrega un Item a un Sale como BienServicioCargado o actualiza la cantidad si ya existe.
     *
     * Lógica de negocio:
     * 1. Si ya existe BienServicioCargado para el Item → incrementa cantidad (ignora precioInicial)
     * 2. Si no existe → crea nuevo BienServicioCargado
     * 3. Si precioInicial es null → usa precioVentaPublico del Item
     * 4. Recalcula totales del Sale automáticamente
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Si falla cualquier paso, toda la operación se revierte automáticamente.
     *
     * @param saleId ID del Sale a actualizar
     * @param itemCodCompuesto Código compuesto del Item a agregar
     * @param precioInicial Precio inicial unitario (si es null usa precioVentaPublico del Item)
     * @param cantidad Cantidad a agregar (si es null usa 1.0)
     * @return Mono<Boolean> true si la operación fue exitosa, false en caso de error
     */
    public Mono<OperationResponse> addItemToSale(UUID saleId, String itemCodCompuesto, Double precioInicial, Double cantidad) {
        log.debug("Adding Item to Sale with id: {}, itemCodCompuesto: {}", saleId, itemCodCompuesto);
        var request = new AddItemToSaleRequest(saleId, itemCodCompuesto, precioInicial, cantidad);
        return connectionService.authenticatedRequest(ROUTE_ADD_ITEM, request, OperationResponse.class);
    }

    /**
     * Actualiza los campos de un BienServicioCargado.
     * Recalcula montoAcordado = precioAcordado * cantidad y actualiza totales del Sale.
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Si falla cualquier paso, toda la operación se revierte automáticamente.
     *
     * @param saleId ID del Sale a actualizar
     * @param bienServicioCargadoId ID del BienServicioCargado a actualizar
     * @param precioAcordado Nuevo precio acordado unitario
     * @param cantidad Nueva cantidad (opcional, si es null no se actualiza)
     * @param descripcionDelBienServicio Nueva descripción (opcional, si es null no se actualiza)
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<OperationResponse> updateBienServicioCargado(UUID saleId, UUID bienServicioCargadoId, Double precioAcordado, Double cantidad, String descripcionDelBienServicio) {
        log.debug("Updating BienServicioCargado in Sale with id: {}, itemCodCompuesto: {}, precioAcordado: {}",
                saleId, bienServicioCargadoId, precioAcordado);
        var request = new UpdateBienServicioCargadoRequest(saleId, bienServicioCargadoId, precioAcordado, cantidad, descripcionDelBienServicio);
        return connectionService.authenticatedRequest(ROUTE_UPDATE_BIEN_SERVICIO_CARGADO, request, OperationResponse.class);
    }

    /**
     * Elimina un BienServicioCargado y actualiza totalMontoInicial, totalMontoAcordado.
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Si falla cualquier paso, toda la operación se revierte automáticamente.
     *
     * @param saleId ID del Sale a actualizar
     * @param bienServicioCargadoId ID del BienServicioCargado a eliminar
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<OperationResponse> deleteBienServicioCargado(UUID saleId, UUID bienServicioCargadoId) {
        log.debug("Deleting BienServicioCargado from Sale with id: {}, bienServicioCargadoId: {}", saleId, bienServicioCargadoId);
        var request = new DeleteBienServicioCargadoRequest(saleId, bienServicioCargadoId);
        return connectionService.authenticatedRequest(ROUTE_DELETE_BIEN_SERVICIO_CARGADO, request, OperationResponse.class);
    }

    /**
     * Elimina todos los nodos BienServicioCargado de un Sale y actualiza totalMontoInicial, totalMontoAcordado.
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Si falla cualquier paso, toda la operación se revierte automáticamente.
     *
     * @param saleId ID del Sale a actualizar
     * @return Mono<Boolean> que indica si la operación fue exitosa
     */
    public Mono<OperationResponse> deleteAllBienServicioCargado(UUID saleId) {
        log.debug("Deleting all BienServicioCargado from Sale with id: {}", saleId);
        var request = new DeleteAllBienServicioCargadoRequest(saleId);
        return connectionService.authenticatedRequest(ROUTE_DELETE_ALL_BIEN_SERVICIO_CARGADO, request, OperationResponse.class);
    }

    /**
     * Inicia una venta de contado.
     *
     * Lógica de negocio:
     * 1. Verifica que exista al menos un BienServicioCargado
     * 2. Recalcula totales del Sale
     * 3. Actualiza campos del Sale (tipoVenta=CONTADO, estaPagadoEntregado=false, totalRestante=totalMontoAcordado)
     * 4. Crea CobroDineroProgramado con límite de 30 minutos
     * 5. Crea relación Sale-CobroDineroProgramado
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Si falla cualquier paso, toda la operación se revierte automáticamente.
     *
     * @param saleId ID del Sale a iniciar como venta de contado
     * @return Mono<Boolean> true si la operación fue exitosa, false en caso de error
     */
    public Mono<OperationResponse> iniciarVentaContado(UUID saleId) {
        log.debug("Initiating venta contado for Sale with id: {}", saleId);
        var request = new IniciarVentaContadoRequest(saleId);
        return connectionService.authenticatedRequest(ROUTE_INICIAR_VENTA_CONTADO, request, OperationResponse.class);
    }

    /**
     * Inicia un BienServicioDevuelto modificando el BienServicioCargado correspondiente y creando un DevolucionDinero.
     *
     * Lógica de negocio:
     * 1. Valida que cantidad y precioAcordadoDevolver estén presentes y cantidad > 0
     * 2. Valida que cantidad a devolver <= cantidad del BienServicioCargado
     * 3. Completa campos del BienServicioDevuelto (devueltoPor, descripción, montoDevuelto, etc.)
     * 4. Guarda BienServicioDevuelto y crea relaciones con Sale e Item
     * 5. Modifica BienServicioCargado: reduce cantidad o lo elimina si cantidad devuelta = cantidad cargada
     * 6. Crea DevolucionDinero con montoADevolver = BienServicioDevuelto.montoDevuelto
     * 7. Recalcula totales del Sale
     *
     * OPERACIÓN TRANSACCIONAL: Usa TransactionalOperator para garantizar consistencia.
     * Si falla cualquier paso, toda la operación se revierte automáticamente.
     *
     * @param saleId ID del Sale
     * @param bienServicioCargadoId ID del BienServicioCargado a devolver
     * @param bienServicioDevuelto BienServicioDevuelto parcialmente inicializado (requiere cantidad y precioAcordadoDevolver)
     * @return Mono<Boolean> true si la operación fue exitosa, false en caso de error
     */
    public Mono<OperationResponse> iniciarBienServicioDevuelto(UUID saleId, UUID bienServicioCargadoId, corp.jamaro.jamaroescritoriofx.appfx.ventas.model.BienServicioDevuelto bienServicioDevuelto) {
        log.debug("Initiating BienServicioDevuelto for Sale with id: {}, bienServicioCargadoId: {}", saleId, bienServicioCargadoId);
        var request = new IniciarBienServicioDevueltoRequest(saleId, bienServicioCargadoId, bienServicioDevuelto);
        return connectionService.authenticatedRequest(ROUTE_INICIAR_BIEN_SERVICIO_DEVUELTO, request, OperationResponse.class);
    }

    // Records para los parámetros de las solicitudes y respuestas

    /**
     * Datos para la solicitud de actualización de Cliente.
     */
    public record UpdateClienteRequest(UUID saleId, UUID clienteId) {}

    /**
     * Datos para la solicitud de agregar un Item a un Sale.
     */
    public record AddItemToSaleRequest(UUID saleId, String itemCodCompuesto, Double precioInicial, Double cantidad) {}

    /**
     * Datos para la solicitud de actualizar los campos de un BienServicioCargado.
     */
    public record UpdateBienServicioCargadoRequest(UUID saleId, UUID bienServicioCargadoId, Double precioAcordado, Double cantidad, String descripcionDelBienServicio) {}

    /**
     * Datos para la solicitud de eliminar un BienServicioCargado.
     */
    public record DeleteBienServicioCargadoRequest(UUID saleId, UUID bienServicioCargadoId) {}

    /**
     * Datos para la solicitud de eliminar todos los BienServicioCargado de un Sale.
     */
    public record DeleteAllBienServicioCargadoRequest(UUID saleId) {}

    /**
     * Datos para la solicitud de iniciar venta de contado.
     */
    public record IniciarVentaContadoRequest(UUID saleId) {}

    /**
     * Datos para la solicitud de iniciar BienServicioDevuelto.
     */
    public record IniciarBienServicioDevueltoRequest(UUID saleId, UUID bienServicioCargadoId, corp.jamaro.jamaroescritoriofx.appfx.ventas.model.BienServicioDevuelto bienServicioDevuelto) {}

    /**
     * Respuesta estándar para operaciones.
     */
    public record OperationResponse(boolean success, String message) {}
}
