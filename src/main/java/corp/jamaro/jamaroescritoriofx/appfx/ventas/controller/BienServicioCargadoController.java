package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.CodigoFabrica;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Grupo;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Ubicacion;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.BienServicioCargado;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.SaleService;
import javafx.application.Platform;
import javafx.beans.property.BooleanProperty;
import javafx.beans.property.SimpleBooleanProperty;
import javafx.fxml.FXML;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.input.KeyCode;
import javafx.scene.input.MouseButton;
import javafx.scene.input.MouseEvent;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.CustomTextField;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.text.NumberFormat;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * Controlador para la vista de un BienServicioCargado en el ListView de SaleController.
 * Cada celda del ListView representa un item cargado en la venta.
 */
@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class BienServicioCargadoController extends BaseController {

    private final SaleService saleService;

    // Componentes básicos siempre visibles
    @FXML
    private AnchorPane anchorPrecioCantidad;

    @FXML
    private AnchorPane anchorBasicContent;

    @FXML
    private CustomTextField cantidad;

    @FXML
    private Label lblCodCompuesto;

    @FXML
    private CustomTextField descripcionDelBienServicio;

    @FXML
    private Label lblDescripcionDisplay;

    @FXML
    private StackPane stackDescripcion;

    @FXML
    private Label lblGrupoItem;

    @FXML
    private Label lblMarca;

    @FXML
    private CustomTextField montoAcordado;

    @FXML
    private CustomTextField precioAcordado;

    @FXML
    private VBox rootBienServicioCargado;

    @FXML
    private VBox vbDatosItem;

    @FXML
    private Label lblStockTotal;

    // Componentes del panel expandido
    @FXML
    private VBox vbExpandedContent;

    @FXML
    private Label lblPrecioInicial;

    @FXML
    private Label lblVehiculo;

    @FXML
    private Label lblPrecioVentaBase;

    @FXML
    private Label lblCodigosFabrica;

    @FXML
    private Label lblUbicaciones;

    @FXML
    private Label lblCargadoPor;

    // Estado y propiedades
    private ContextMenu contextMenu;
    private NumberFormat decimalFormat;
    private BienServicioCargado bienServicioCargado;
    private UUID saleId;
    private final BooleanProperty expanded = new SimpleBooleanProperty(false);

    // Alturas para el cálculo de expansión - Calculadas dinámicamente
    private static final double COLLAPSED_HEIGHT = 75.0; // Altura base mejorada
    private double expandedHeight = -1; // Se calcula dinámicamente

    /**
     * Inicializa el formato decimal para mostrar precios sin el símbolo de moneda.
     */
    private void initializeDecimalFormat() {
        decimalFormat = NumberFormat.getNumberInstance();
        decimalFormat.setMinimumFractionDigits(2);
        decimalFormat.setMaximumFractionDigits(2);
    }

    /**
     * Configura el controlador con un BienServicioCargado y actualiza la UI.
     * @param bienServicioCargado El BienServicioCargado a mostrar
     * @param saleId ID de la venta para las actualizaciones
     */
    public void setBienServicioCargado(BienServicioCargado bienServicioCargado, UUID saleId) {
        this.bienServicioCargado = bienServicioCargado;
        this.saleId = saleId;
        updateUI();
    }

    /**
     * Obtiene la altura actual del elemento (expandido o contraído).
     * Calcula dinámicamente la altura expandida si es necesario.
     * @return La altura actual del elemento
     */
    public double getCurrentHeight() {
        if (expanded.get()) {
            if (expandedHeight == -1) {
                calculateExpandedHeight();
            }
            return expandedHeight;
        }
        return COLLAPSED_HEIGHT;
    }

    /**
     * Calcula dinámicamente la altura expandida basándose en el contenido.
     */
    private void calculateExpandedHeight() {
        if (vbExpandedContent == null) {
            expandedHeight = COLLAPSED_HEIGHT + 60; // Fallback
            return;
        }

        // Hacer visible temporalmente para calcular la altura
        boolean wasVisible = vbExpandedContent.isVisible();
        boolean wasManaged = vbExpandedContent.isManaged();

        vbExpandedContent.setVisible(true);
        vbExpandedContent.setManaged(true);

        // Forzar layout para obtener medidas precisas
        vbExpandedContent.applyCss();
        vbExpandedContent.layout();

        // Calcular altura total
        double contentHeight = vbExpandedContent.prefHeight(-1);
        if (contentHeight <= 0) {
            contentHeight = 60; // Altura mínima por defecto
        }

        expandedHeight = COLLAPSED_HEIGHT + contentHeight + 10; // +10 para padding

        // Restaurar estado original
        vbExpandedContent.setVisible(wasVisible);
        vbExpandedContent.setManaged(wasManaged);

        log.debug("Altura expandida calculada: {} para BienServicioCargado {}",
                 expandedHeight,
                 bienServicioCargado != null && bienServicioCargado.getItem() != null ?
                 bienServicioCargado.getItem().getCodCompuesto() : "N/A");
    }

    /**
     * Verifica si el elemento está expandido.
     * @return true si está expandido, false si está contraído
     */
    public boolean isExpanded() {
        return expanded.get();
    }

    /**
     * Actualiza la interfaz de usuario con los datos del BienServicioCargado.
     */
    private void updateUI() {
        if (bienServicioCargado == null) {
            log.warn("No se puede actualizar UI: bienServicioCargado es null");
            return;
        }

        // Aplicar estilo según el stock
        applyStockStyling();

        // Mostrar código compuesto
        if (bienServicioCargado.getItem() != null && bienServicioCargado.getItem().getCodCompuesto() != null) {
            lblCodCompuesto.setText(bienServicioCargado.getItem().getCodCompuesto());
        } else {
            lblCodCompuesto.setText("N/A");
        }

        // Mostrar marca
        if (bienServicioCargado.getItem() != null && bienServicioCargado.getItem().getMarca() != null) {
            lblMarca.setText(bienServicioCargado.getItem().getMarca().getNombre());
        } else {
            lblMarca.setText("");
        }

        // Mostrar grupo del item
        if (bienServicioCargado.getItem() != null && bienServicioCargado.getItem().getProductos() != null) {
            // Intentar obtener el grupo del primer producto asociado al item
            bienServicioCargado.getItem().getProductos().stream()
                    .findFirst()
                    .ifPresent(producto -> {
                        Set<Grupo> grupos = producto.getGrupos();
                        if (grupos != null && !grupos.isEmpty()) {
                            grupos.stream().findFirst().ifPresent(grupo ->
                                lblGrupoItem.setText(grupo.getNombrePrincipal())
                            );
                        } else {
                            lblGrupoItem.setText("");
                        }
                    });
        } else {
            lblGrupoItem.setText("");
        }

        // Mostrar descripción en Camel Case - usar descripcionDelBienServicio si está disponible
        String descripcionToShow = "";
        if (bienServicioCargado.getDescripcionDelBienServicio() != null && !bienServicioCargado.getDescripcionDelBienServicio().trim().isEmpty()) {
            descripcionToShow = bienServicioCargado.getDescripcionDelBienServicio();
        } else if (bienServicioCargado.getItem() != null && bienServicioCargado.getItem().getDescripcion() != null) {
            descripcionToShow = bienServicioCargado.getItem().getDescripcion();
        }
        String descripcionFormateada = toCamelCase(descripcionToShow);
        lblDescripcionDisplay.setText(descripcionFormateada);
        descripcionDelBienServicio.setText(descripcionFormateada);

        // Mostrar precio acordado sin símbolo de moneda
        if (bienServicioCargado.getPrecioAcordado() != null) {
            precioAcordado.setText(decimalFormat.format(bienServicioCargado.getPrecioAcordado()));
        } else if (bienServicioCargado.getPrecioInicial() != null) {
            // Si no hay precio acordado, usar el precio inicial
            precioAcordado.setText(decimalFormat.format(bienServicioCargado.getPrecioInicial()));
        } else {
            precioAcordado.setText(decimalFormat.format(0));
        }

        // Mostrar cantidad con ceros adelante (formato 3 dígitos)
        if (bienServicioCargado.getCantidad() != null) {
            int cantidadValue = bienServicioCargado.getCantidad().intValue();
            cantidad.setText(String.format("%03d", cantidadValue));
        } else {
            cantidad.setText("000");
        }

        // Mostrar total (cantidad x precio) sin símbolo de moneda
        if (bienServicioCargado.getMontoAcordado() != null) {
            montoAcordado.setText(decimalFormat.format(bienServicioCargado.getMontoAcordado()));
        } else {
            // Calcular el total si no está disponible
            double precio = bienServicioCargado.getPrecioAcordado() != null ?
                    bienServicioCargado.getPrecioAcordado() :
                    (bienServicioCargado.getPrecioInicial() != null ? bienServicioCargado.getPrecioInicial() : 0);
            double cantidadValue = bienServicioCargado.getCantidad() != null ? bienServicioCargado.getCantidad() : 0;
            montoAcordado.setText(decimalFormat.format(precio * cantidadValue));
        }

        // Actualizar información expandida
        updateExpandedInfo();
    }

    /**
     * Actualiza la información del panel expandido.
     */
    private void updateExpandedInfo() {
        if (bienServicioCargado == null || bienServicioCargado.getItem() == null) {
            return;
        }

        // Precio inicial
        if (bienServicioCargado.getPrecioInicial() != null) {
            lblPrecioInicial.setText("Precio Inicial: " + decimalFormat.format(bienServicioCargado.getPrecioInicial()));
        } else {
            lblPrecioInicial.setText("Precio Inicial: N/A");
        }

        // Precio venta base del item
        if (bienServicioCargado.getItem().getPrecioVentaBase() != null) {
            lblPrecioVentaBase.setText("Precio Venta Base: " + decimalFormat.format(bienServicioCargado.getItem().getPrecioVentaBase()));
        } else {
            lblPrecioVentaBase.setText("Precio Venta Base: N/A");
        }

        // Vehículo (del primer producto asociado)
        String vehiculo = "N/A";
        if (bienServicioCargado.getItem().getProductos() != null) {
            vehiculo = bienServicioCargado.getItem().getProductos().stream()
                    .findFirst()
                    .flatMap(producto -> producto.getVehiculos() != null && !producto.getVehiculos().isEmpty() ?
                            producto.getVehiculos().stream().findFirst() : Optional.empty())
                    .flatMap(vehiculoObj -> vehiculoObj.getNombres() != null && !vehiculoObj.getNombres().isEmpty() ?
                            vehiculoObj.getNombres().stream().findFirst() : Optional.empty())
                    .map(vehiculoNombre -> vehiculoNombre.getNombre())
                    .orElse("N/A");
        }
        lblVehiculo.setText("Vehículo: " + vehiculo);

        // Códigos de fábrica
        String codigosFabrica = "N/A";
        if (bienServicioCargado.getItem().getCodigosFabrica() != null && !bienServicioCargado.getItem().getCodigosFabrica().isEmpty()) {
            codigosFabrica = bienServicioCargado.getItem().getCodigosFabrica().stream()
                    .map(CodigoFabrica::getCodigo)
                    .collect(Collectors.joining(", "));
        }
        lblCodigosFabrica.setText("Códigos: " + codigosFabrica);

        // Ubicaciones
        String ubicaciones = "N/A";
        if (bienServicioCargado.getItem().getUbicaciones() != null && !bienServicioCargado.getItem().getUbicaciones().isEmpty()) {
            ubicaciones = bienServicioCargado.getItem().getUbicaciones().stream()
                    .map(Ubicacion::getNombre)
                    .collect(Collectors.joining(", "));
        }
        lblUbicaciones.setText("Ubicaciones: " + ubicaciones);

        // Stock total
        if (bienServicioCargado.getItem().getStockTotal() != null) {
            lblStockTotal.setText("Total: " + bienServicioCargado.getItem().getStockTotal().intValue());
        } else {
            lblStockTotal.setText("Total: 0");
        }

        // Cargado por
        if (bienServicioCargado.getCargadoPor() != null && !bienServicioCargado.getCargadoPor().trim().isEmpty()) {
            lblCargadoPor.setText("Por: " + bienServicioCargado.getCargadoPor());
        } else {
            lblCargadoPor.setText("Por: N/A");
        }
    }

    /**
     * Aplica estilos según el nivel de stock del item.
     * Items con stock cero o negativo tendrán un fondo de color de error.
     */
    private void applyStockStyling() {
        // Limpiar estilos previos
        rootBienServicioCargado.getStyleClass().removeAll("stock-error");

        // Verificar si el item tiene stock cero o negativo
        if (bienServicioCargado.getItem() != null &&
            bienServicioCargado.getItem().getStockTotal() != null &&
            bienServicioCargado.getItem().getStockTotal() <= 0) {
            // Aplicar estilo de error para stock cero o negativo
            rootBienServicioCargado.getStyleClass().add("stock-error");
        }
    }

    /**
     * Maneja el clic en el elemento para expandir/contraer o mostrar menú contextual.
     */
    @FXML
    private void handleClick(MouseEvent event) {
        if (event.getButton() == MouseButton.PRIMARY && event.getClickCount() == 1) {
            // Clic izquierdo simple: expandir/contraer
            toggleExpanded();
            event.consume();
        } else if (event.getButton() == MouseButton.SECONDARY) {
            // Clic derecho: mostrar menú contextual
            showContextMenu(event);
            event.consume();
        }
    }

    /**
     * Alterna el estado de expansión del elemento.
     * Implementa cálculo dinámico de altura similar a ProductoItemSearchedController.
     */
    private void toggleExpanded() {
        boolean newExpandedState = !expanded.get();
        expanded.set(newExpandedState);

        if (newExpandedState) {
            // Expandir
            vbExpandedContent.setVisible(true);
            vbExpandedContent.setManaged(true);

            // Agregar clase CSS para estado expandido
            if (!rootBienServicioCargado.getStyleClass().contains("bien-servicio-cargado-expanded")) {
                rootBienServicioCargado.getStyleClass().add("bien-servicio-cargado-expanded");
            }

            // Calcular altura expandida si no se ha calculado
            if (expandedHeight == -1) {
                calculateExpandedHeight();
            }

            // Aplicar altura calculada
            rootBienServicioCargado.setPrefHeight(expandedHeight);
            rootBienServicioCargado.setMinHeight(expandedHeight);
            rootBienServicioCargado.setMaxHeight(expandedHeight);
        } else {
            // Contraer
            vbExpandedContent.setVisible(false);
            vbExpandedContent.setManaged(false);

            // Remover clase CSS de estado expandido
            rootBienServicioCargado.getStyleClass().remove("bien-servicio-cargado-expanded");

            // Volver a altura contraída
            rootBienServicioCargado.setPrefHeight(COLLAPSED_HEIGHT);
            rootBienServicioCargado.setMinHeight(COLLAPSED_HEIGHT);
            rootBienServicioCargado.setMaxHeight(COLLAPSED_HEIGHT);
        }

        // Forzar actualización del layout
        runOnUiThread(() -> {
            rootBienServicioCargado.requestLayout();
            rootBienServicioCargado.applyCss();
            rootBienServicioCargado.layout();

            // Actualización adicional después de un breve retraso
            Platform.runLater(() -> {
                if (newExpandedState) {
                    vbExpandedContent.setVisible(true);
                    vbExpandedContent.setManaged(true);
                }
                rootBienServicioCargado.layout();
            });
        });

        log.debug("BienServicioCargado {} {} - Altura: {}",
                bienServicioCargado != null && bienServicioCargado.getItem() != null ?
                bienServicioCargado.getItem().getCodCompuesto() : "N/A",
                newExpandedState ? "expandido" : "contraído",
                getCurrentHeight());
    }

    /**
     * Muestra el menú contextual con opciones para el BienServicioCargado.
     */
    private void showContextMenu(MouseEvent event) {
        if (contextMenu == null) {
            createContextMenu();
        }

        // Mostrar el menú en la posición del clic
        contextMenu.show(rootBienServicioCargado, event.getScreenX(), event.getScreenY());
    }

    /**
     * Crea el menú contextual con las opciones disponibles.
     */
    private void createContextMenu() {
        contextMenu = new ContextMenu();
        contextMenu.getStyleClass().add("context-menu");

        // Opción para eliminar el BienServicioCargado
        MenuItem deleteItem = new MenuItem("Eliminar");
        deleteItem.getStyleClass().add("delete-menu-item");
        deleteItem.setOnAction(event -> deleteBienServicioCargado());

        // Opción para expandir/contraer
        MenuItem toggleItem = new MenuItem(expanded.get() ? "Contraer" : "Expandir");
        toggleItem.setOnAction(event -> toggleExpanded());

        // Actualizar texto del toggle cuando cambie el estado
        expanded.addListener((obs, oldVal, newVal) -> {
            toggleItem.setText(newVal ? "Contraer" : "Expandir");
        });

        contextMenu.getItems().addAll(toggleItem, deleteItem);
    }

    /**
     * Elimina el BienServicioCargado actual de la venta.
     */
    private void deleteBienServicioCargado() {
        if (saleId == null || bienServicioCargado == null || bienServicioCargado.getId() == null) {
            log.warn("No se puede eliminar BienServicioCargado: datos insuficientes");
            return;
        }

        log.debug("Eliminando BienServicioCargado {} de la venta {}",
                 bienServicioCargado.getId(), saleId);

        // Llamar al servicio para eliminar
        saleService.deleteBienServicioCargado(saleId, bienServicioCargado.getId())
                .subscribe(
                        response -> {
                            if (response.success()) {
                                log.info("BienServicioCargado eliminado exitosamente: {}",
                                        bienServicioCargado.getId());
                                // No necesitamos actualizar la UI aquí, ya que recibiremos
                                // una actualización a través de la suscripción en SaleController
                            } else {
                                log.warn("Error al eliminar BienServicioCargado: {}", response.message());
                                runOnUiThread(() ->
                                    alertUtil.showError("Error al eliminar el item: " + response.message())
                                );
                            }
                        },
                        error -> {
                            log.error("Error al eliminar BienServicioCargado: {}", error.getMessage(), error);
                            runOnUiThread(() ->
                                alertUtil.showError("Error al eliminar el item: " + error.getMessage())
                            );
                        }
                );
    }

    /**
     * Configura un CustomTextField para que actúe como un label editable.
     */
    private void configureEditableField(CustomTextField field, Runnable onEdit) {
        // Hacer que parezca un label por defecto
        field.setEditable(false);
        field.setFocusTraversable(false);

        // Evitar que se muestre el foco visual
        field.setStyle("-fx-focus-color: transparent; -fx-faint-focus-color: transparent;");

        // Habilitar edición SOLO con doble clic
        field.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2) {
                field.setEditable(true);
                field.setFocusTraversable(true);
                // Restaurar el estilo de foco cuando está en modo edición
                field.setStyle("");
                field.requestFocus();
                field.selectAll();
                event.consume();
            } else {
                // Clic simple no debe hacer nada para evitar ediciones accidentales
                // Pero tampoco debe tomar el foco
                event.consume();
            }
        });

        // Confirmar edición con Enter
        field.setOnKeyPressed(event -> {
            if (field.isEditable()) {
                if (event.getCode() == KeyCode.ENTER) {
                    finishEditing(field, onEdit);
                    event.consume();
                } else if (event.getCode() == KeyCode.ESCAPE) {
                    cancelEditing(field);
                    event.consume();
                }
            }
        });

        // Confirmar edición al perder el foco
        field.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal && field.isEditable()) {
                finishEditing(field, onEdit);
            }
        });
    }

    /**
     * Finaliza la edición de un campo.
     */
    private void finishEditing(CustomTextField field, Runnable onEdit) {
        field.setEditable(false);
        field.setFocusTraversable(false);
        // Restaurar el estilo sin foco
        field.setStyle("-fx-focus-color: transparent; -fx-faint-focus-color: transparent;");
        onEdit.run();
    }

    /**
     * Cancela la edición de un campo.
     */
    private void cancelEditing(CustomTextField field) {
        field.setEditable(false);
        field.setFocusTraversable(false);
        // Restaurar el estilo sin foco
        field.setStyle("-fx-focus-color: transparent; -fx-faint-focus-color: transparent;");
        updateUI(); // Restaurar valor original
    }

    /**
     * Convierte un texto a formato Camel Case.
     * Ejemplo: "RODAJE DEL PROBOX" -> "Rodaje Del Probox"
     *
     * @param text El texto a convertir
     * @return El texto en formato Camel Case
     */
    private String toCamelCase(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // Dividir el texto en palabras
        String[] words = text.toLowerCase().split("\\s+");
        StringBuilder camelCase = new StringBuilder();

        // Convertir cada palabra a Camel Case
        for (String word : words) {
            if (!word.isEmpty()) {
                camelCase.append(Character.toUpperCase(word.charAt(0)))
                         .append(word.substring(1))
                         .append(" ");
            }
        }

        // Eliminar el espacio final y devolver el resultado
        return camelCase.toString().trim();
    }

    /**
     * Método de inicialización llamado por JavaFX después de que todos los elementos @FXML
     * han sido inyectados. Implementación requerida por la interfaz Initializable.
     *
     * @param url La ubicación utilizada para resolver rutas relativas para el objeto raíz
     * @param resourceBundle Los recursos utilizados para localizar el objeto raíz
     */
    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // Inicializar el formato decimal
        initializeDecimalFormat();

        // Configurar campos editables
        configureEditableFields();

        // Configurar layout responsivo
        configureResponsiveLayout();

        log.debug("BienServicioCargadoController inicializado");
    }

    /**
     * Configura los campos editables con doble clic.
     */
    private void configureEditableFields() {
        // Configurar descripción editable híbrida
        configureDescripcionEditable();

        // Configurar precio acordado editable
        configureEditableField(precioAcordado, this::updatePrecioAcordado);

        // Configurar cantidad editable
        configureEditableField(cantidad, this::updateCantidad);

        // Configurar monto acordado editable
        configureEditableField(montoAcordado, this::updateMontoAcordado);
    }

    /**
     * Configura la descripción editable híbrida (Label + CustomTextField).
     */
    private void configureDescripcionEditable() {
        // Configurar el CustomTextField
        descripcionDelBienServicio.setEditable(false);
        descripcionDelBienServicio.setFocusTraversable(false);
        descripcionDelBienServicio.setStyle("-fx-focus-color: transparent; -fx-faint-focus-color: transparent;");

        // Configurar el evento de doble clic en el Label
        lblDescripcionDisplay.setOnMouseClicked(event -> {
            if (event.getClickCount() == 2) {
                startDescripcionEditing();
                event.consume();
            }
        });

        // Configurar eventos del CustomTextField
        descripcionDelBienServicio.setOnKeyPressed(event -> {
            if (descripcionDelBienServicio.isEditable()) {
                if (event.getCode() == KeyCode.ENTER) {
                    finishDescripcionEditing();
                    event.consume();
                } else if (event.getCode() == KeyCode.ESCAPE) {
                    cancelDescripcionEditing();
                    event.consume();
                }
            }
        });

        descripcionDelBienServicio.focusedProperty().addListener((obs, oldVal, newVal) -> {
            if (!newVal && descripcionDelBienServicio.isEditable()) {
                finishDescripcionEditing();
            }
        });
    }

    /**
     * Inicia la edición de la descripción.
     */
    private void startDescripcionEditing() {
        lblDescripcionDisplay.setVisible(false);
        descripcionDelBienServicio.setVisible(true);
        descripcionDelBienServicio.setEditable(true);
        descripcionDelBienServicio.setFocusTraversable(true);
        descripcionDelBienServicio.setStyle("");
        descripcionDelBienServicio.requestFocus();
        descripcionDelBienServicio.selectAll();
    }

    /**
     * Finaliza la edición de la descripción.
     */
    private void finishDescripcionEditing() {
        descripcionDelBienServicio.setEditable(false);
        descripcionDelBienServicio.setFocusTraversable(false);
        descripcionDelBienServicio.setStyle("-fx-focus-color: transparent; -fx-faint-focus-color: transparent;");
        descripcionDelBienServicio.setVisible(false);
        lblDescripcionDisplay.setVisible(true);

        // Actualizar el texto del label
        lblDescripcionDisplay.setText(descripcionDelBienServicio.getText());

        // Llamar al método de actualización
        updateDescripcion();
    }

    /**
     * Cancela la edición de la descripción.
     */
    private void cancelDescripcionEditing() {
        descripcionDelBienServicio.setEditable(false);
        descripcionDelBienServicio.setFocusTraversable(false);
        descripcionDelBienServicio.setStyle("-fx-focus-color: transparent; -fx-faint-focus-color: transparent;");
        descripcionDelBienServicio.setVisible(false);
        lblDescripcionDisplay.setVisible(true);

        // Restaurar el valor original
        updateUI();
    }

    /**
     * Actualiza la descripción del bien servicio.
     */
    private void updateDescripcion() {
        if (saleId == null || bienServicioCargado == null) {
            log.warn("No se puede actualizar descripción: datos insuficientes");
            return;
        }

        String nuevaDescripcion = descripcionDelBienServicio.getText();

        saleService.updateBienServicioCargado(
                saleId,
                bienServicioCargado.getId(),
                bienServicioCargado.getPrecioAcordado(),
                bienServicioCargado.getCantidad(),
                nuevaDescripcion
        ).subscribe(
                response -> log.debug("Descripción actualizada exitosamente"),
                error -> {
                    log.error("Error al actualizar descripción: {}", error.getMessage());
                    runOnUiThread(this::updateUI); // Restaurar valor original
                }
        );
    }

    /**
     * Actualiza el precio acordado.
     */
    private void updatePrecioAcordado() {
        if (saleId == null || bienServicioCargado == null) {
            log.warn("No se puede actualizar precio acordado: datos insuficientes");
            return;
        }

        try {
            String precioText = precioAcordado.getText().replace(",", "");
            Double nuevoPrecio = Double.parseDouble(precioText);

            saleService.updateBienServicioCargado(
                    saleId,
                    bienServicioCargado.getId(),
                    nuevoPrecio,
                    bienServicioCargado.getCantidad(),
                    bienServicioCargado.getDescripcionDelBienServicio()
            ).subscribe(
                    response -> log.debug("Precio acordado actualizado exitosamente"),
                    error -> {
                        log.error("Error al actualizar precio acordado: {}", error.getMessage());
                        runOnUiThread(this::updateUI); // Restaurar valor original
                    }
            );
        } catch (NumberFormatException e) {
            log.warn("Formato de precio inválido: {}", precioAcordado.getText());
            updateUI(); // Restaurar valor original
        }
    }

    /**
     * Actualiza la cantidad.
     */
    private void updateCantidad() {
        if (saleId == null || bienServicioCargado == null) {
            log.warn("No se puede actualizar cantidad: datos insuficientes");
            return;
        }

        try {
            String cantidadText = cantidad.getText();
            Double nuevaCantidad = Double.parseDouble(cantidadText);

            saleService.updateBienServicioCargado(
                    saleId,
                    bienServicioCargado.getId(),
                    bienServicioCargado.getPrecioAcordado(),
                    nuevaCantidad,
                    bienServicioCargado.getDescripcionDelBienServicio()
            ).subscribe(
                    response -> log.debug("Cantidad actualizada exitosamente"),
                    error -> {
                        log.error("Error al actualizar cantidad: {}", error.getMessage());
                        runOnUiThread(this::updateUI); // Restaurar valor original
                    }
            );
        } catch (NumberFormatException e) {
            log.warn("Formato de cantidad inválido: {}", cantidad.getText());
            updateUI(); // Restaurar valor original
        }
    }

    /**
     * Actualiza el monto acordado y calcula automáticamente el precio acordado.
     */
    private void updateMontoAcordado() {
        if (saleId == null || bienServicioCargado == null) {
            log.warn("No se puede actualizar monto acordado: datos insuficientes");
            return;
        }

        try {
            String montoText = montoAcordado.getText().replace(",", "");
            Double nuevoMonto = Double.parseDouble(montoText);
            Double cantidadActual = bienServicioCargado.getCantidad();

            if (cantidadActual == null || cantidadActual <= 0) {
                log.warn("No se puede calcular precio acordado: cantidad es cero o nula");
                updateUI(); // Restaurar valor original
                return;
            }

            // Calcular el nuevo precio acordado dividiendo el monto entre la cantidad
            Double nuevoPrecioAcordado = nuevoMonto / cantidadActual;

            saleService.updateBienServicioCargado(
                    saleId,
                    bienServicioCargado.getId(),
                    nuevoPrecioAcordado,
                    cantidadActual,
                    bienServicioCargado.getDescripcionDelBienServicio()
            ).subscribe(
                    response -> log.debug("Monto acordado actualizado exitosamente"),
                    error -> {
                        log.error("Error al actualizar monto acordado: {}", error.getMessage());
                        runOnUiThread(this::updateUI); // Restaurar valor original
                    }
            );
        } catch (NumberFormatException e) {
            log.warn("Formato de monto inválido: {}", montoAcordado.getText());
            updateUI(); // Restaurar valor original
        }
    }

    /**
     * Configura el layout para que sea responsivo cuando cambia el tamaño disponible.
     * Asegura que la descripción se ajuste correctamente y que los elementos de precio
     * y cantidad siempre sean visibles.
     */
    private void configureResponsiveLayout() {
        // Asegurar que el AnchorPane padre ajuste correctamente sus hijos
        rootBienServicioCargado.widthProperty().addListener((obs, oldVal, newVal) -> {
            double availableWidth = newVal.doubleValue();

            // Recalcular altura expandida cuando cambia el ancho
            if (expanded.get()) {
                expandedHeight = -1; // Forzar recálculo
                calculateExpandedHeight();

                // Aplicar nueva altura si está expandido
                rootBienServicioCargado.setPrefHeight(expandedHeight);
                rootBienServicioCargado.setMinHeight(expandedHeight);
                rootBienServicioCargado.setMaxHeight(expandedHeight);
            }

            log.debug("Ancho cambiado a {} para BienServicioCargado {}",
                     availableWidth,
                     bienServicioCargado != null && bienServicioCargado.getItem() != null ?
                     bienServicioCargado.getItem().getCodCompuesto() : "N/A");
        });

        // Configurar el StackPane de descripción para que sea responsivo
        if (stackDescripcion != null) {
            stackDescripcion.widthProperty().addListener((obs, oldVal, newVal) -> {
                // Asegurar que el texto se ajuste correctamente
                double descriptionWidth = newVal.doubleValue() - 10; // Margen
                if (descriptionWidth > 0) {
                    lblDescripcionDisplay.setMaxWidth(descriptionWidth);
                    descripcionDelBienServicio.setMaxWidth(descriptionWidth);
                }
            });
        }
    }

}
