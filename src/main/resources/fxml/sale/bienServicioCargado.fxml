<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>
<?import org.controlsfx.control.textfield.CustomTextField?>

<VBox fx:id="rootBienServicioCargado" styleClass="bien-servicio-cargado" stylesheets="@../../css/sale.css" onMouseClicked="#handleClick" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.BienServicioCargadoController">
   <children>
      <!-- Contenido básico siempre visible - MANTENER DISEÑO ORIGINAL -->
      <AnchorPane fx:id="anchorBasicContent" minHeight="65.0" prefHeight="65.0">
         <children>
            <!-- Panel izquierdo: Datos del item -->
            <VBox fx:id="vbDatosItem" alignment="CENTER" maxWidth="90.0" minWidth="90.0" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.topAnchor="0.0">
               <children>
                  <Label fx:id="lblGrupoItem" alignment="CENTER" maxWidth="88.0" minWidth="88.0" prefWidth="88.0" text="Rodaje" wrapText="true" VBox.vgrow="ALWAYS">
                     <font>
                        <Font size="12.0" />
                     </font>
                     <VBox.margin>
                        <Insets top="3.0" />
                     </VBox.margin>
                  </Label>
                  <Label fx:id="lblCodCompuesto" alignment="CENTER" maxWidth="88.0" minWidth="88.0" prefHeight="45.0" prefWidth="88.0" styleClass="bien-servicio-codigo" text="00158KOY" textAlignment="CENTER" wrapText="true" VBox.vgrow="ALWAYS">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <Label fx:id="lblMarca" alignment="CENTER" maxWidth="88.0" minWidth="88.0" prefWidth="88.0" styleClass="marca-label" text="Koyo" wrapText="true" VBox.vgrow="ALWAYS">
                     <font>
                        <Font size="12.0" />
                     </font>
                     <VBox.margin>
                        <Insets bottom="3.0" />
                     </VBox.margin>
                  </Label>
               </children>
            </VBox>

            <!-- Panel central: Descripción responsiva -->
            <StackPane fx:id="stackDescripcion" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="90.0" AnchorPane.rightAnchor="90.0" AnchorPane.topAnchor="0.0">
               <children>
                  <Label fx:id="lblDescripcionDisplay" alignment="CENTER" maxWidth="Infinity" styleClass="bien-servicio-descripcion" text="Descripcion Producto" wrapText="true" StackPane.alignment="CENTER">
                     <font>
                        <Font size="14.0" />
                     </font>
                     <padding>
                        <Insets left="3.0" right="3.0" />
                     </padding>
                  </Label>
                  <CustomTextField fx:id="descripcionDelBienServicio" maxWidth="Infinity" styleClass="bien-servicio-descripcion, label-like" text="Descripcion Producto" visible="false" StackPane.alignment="CENTER">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </CustomTextField>
               </children>
            </StackPane>

            <!-- Panel derecho: Precios y cantidades -->
            <AnchorPane fx:id="anchorPrecioCantidad" maxHeight="65.0" maxWidth="90.0" minHeight="65.0" minWidth="90.0" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <children>
                  <CustomTextField fx:id="precioAcordado" alignment="CENTER" styleClass="bien-servicio-precio, label-like" text="3.00" AnchorPane.bottomAnchor="30.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="33.0" AnchorPane.topAnchor="0.0">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </CustomTextField>
                  <CustomTextField fx:id="cantidad" alignment="CENTER" prefHeight="24.0" prefWidth="41.0" styleClass="bien-servicio-cantidad, label-like" text="001" AnchorPane.bottomAnchor="30.0" AnchorPane.leftAnchor="58.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                     <font>
                        <Font size="18.0" />
                     </font>
                  </CustomTextField>
                  <CustomTextField fx:id="montoAcordado" alignment="CENTER" styleClass="bien-servicio-total, label-like" text="3.00" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="24.0">
                     <font>
                        <Font size="21.0" />
                     </font>
                  </CustomTextField>
               </children>
            </AnchorPane>
         </children>
      </AnchorPane>

      <!-- Panel expandido: MANTENER COHERENCIA CON DISEÑO ORIGINAL -->
      <VBox fx:id="vbExpandedContent" managed="false" visible="false" styleClass="expanded-content">
         <children>
            <!-- Mantener la misma estructura de 3 columnas -->
            <HBox spacing="0.0" alignment="CENTER">
               <!-- Columna izquierda: Información adicional del item -->
               <VBox alignment="TOP_CENTER" minWidth="90.0" prefWidth="90.0" spacing="3.0">
                  <children>
                     <Label text="Detalles" styleClass="section-header-small">
                        <font>
                           <Font name="System Bold" size="10.0" />
                        </font>
                     </Label>
                     <Label fx:id="lblPrecioInicial" text="P.Inicial: 0.00" styleClass="detail-label-small" wrapText="true">
                        <font>
                           <Font size="9.0" />
                        </font>
                     </Label>
                     <Label fx:id="lblPrecioVentaBase" text="P.Base: 0.00" styleClass="detail-label-small" wrapText="true">
                        <font>
                           <Font size="9.0" />
                        </font>
                     </Label>
                  </children>
                  <padding>
                     <Insets left="5.0" right="5.0" />
                  </padding>
               </VBox>

               <!-- Columna central: Información del vehículo y códigos -->
               <VBox alignment="TOP_LEFT" HBox.hgrow="ALWAYS" spacing="3.0">
                  <children>
                     <Label text="Información del Item" styleClass="section-header-small">
                        <font>
                           <Font name="System Bold" size="10.0" />
                        </font>
                     </Label>
                     <Label fx:id="lblVehiculo" text="Vehículo: N/A" styleClass="detail-label-small" wrapText="true">
                        <font>
                           <Font size="9.0" />
                        </font>
                     </Label>
                     <Label fx:id="lblCodigosFabrica" text="Códigos: N/A" styleClass="detail-label-small" wrapText="true">
                        <font>
                           <Font size="9.0" />
                        </font>
                     </Label>
                     <Label fx:id="lblUbicaciones" text="Ubicaciones: N/A" styleClass="detail-label-small" wrapText="true">
                        <font>
                           <Font size="9.0" />
                        </font>
                     </Label>
                  </children>
                  <padding>
                     <Insets left="3.0" right="3.0" />
                  </padding>
               </VBox>

               <!-- Columna derecha: Espacio para mantener alineación -->
               <VBox alignment="TOP_CENTER" minWidth="90.0" prefWidth="90.0" spacing="3.0">
                  <children>
                     <Label text="Stock" styleClass="section-header-small">
                        <font>
                           <Font name="System Bold" size="10.0" />
                        </font>
                     </Label>
                     <Label fx:id="lblStockTotal" text="Total: 0" styleClass="detail-label-small">
                        <font>
                           <Font size="9.0" />
                        </font>
                     </Label>
                  </children>
                  <padding>
                     <Insets left="5.0" right="5.0" />
                  </padding>
               </VBox>
            </HBox>
         </children>
         <padding>
            <Insets bottom="5.0" left="0.0" right="0.0" top="5.0" />
         </padding>
      </VBox>
   </children>
</VBox>
