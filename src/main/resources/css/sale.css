/*
 * JavaFX CSS - Hoja de estilos específica para la funcionalidad de ventas
 * Este archivo contiene estilos para:
 * - SaleController (solo bienServicioCargados, se eliminó bienServicioDevueltos)
 * - BienServicioCargadoController
 * - bienServicioCargado.fxml
 * - sale.fxml
 */

/* Importar variables de tamaños de fuente */
@import "font-sizes.css";

/* Importar variables globales de colores desde styles.css */
@import "styles.css";

/* ===== Estilos para los ListViews en SaleController ===== */
.sale-list-view {
    -fx-background-color: -fx-control-inner-background;
    -fx-border-color: -fx-primary-color-light;
    -fx-effect: dropshadow(three-pass-box, -fx-shadow-color, 5, 0, 0, 0);
    -fx-padding: 0;
}

/* Ocultar la barra de desplazamiento horizontal en los ListViews */
.sale-list-view .scroll-bar:horizontal {
    -fx-opacity: 0;
    -fx-pref-height: 0;
    -fx-min-height: 0;
    -fx-max-height: 0;
}

/* Estilos para las celdas de los ListViews */
.sale-list-view .list-cell {
    -fx-background-color: -fx-primary-color-dark;
    -fx-padding: 10px 2px;
    /* JavaFX no soporta margin directamente, usamos padding en el ListView */
    /* Eliminamos cualquier transición que pueda causar cambios de tamaño */
    -fx-transition: none;
    -fx-effect: none;
    /* Aseguramos que no haya cambios de tamaño */
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
    -fx-scale-z: 1.0;
}

/* Estilo para celdas vacías - hacerlas transparentes a eventos de mouse */
.sale-list-view .list-cell:empty {
    -fx-background-color: -fx-primary-color-dark;
    -fx-border-width: 0;
    -fx-opacity: 0;
    -fx-mouse-transparent: true;
    -fx-padding: 0;
}

/* Estilo para hover en celdas de ListView, solo cambia el color de fondo sin efectos de sombra */
.sale-list-view .list-cell:hover {
    -fx-background-color: derive(-fx-primary-color-dark, 25%);
    /* Eliminamos cualquier efecto o transformación que pueda causar cambios de tamaño */
    -fx-effect: none;
    -fx-transition: none;
    /* Aseguramos que no haya cambios de tamaño */
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
    -fx-scale-z: 1.0;
}

/* ===== Estilos para BienServicioCargado ===== */
.bien-servicio-cargado {
    -fx-background-color: derive(-fx-primary-color-dark, 15%);
    -fx-padding: 5px 2px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-border-color: derive(-fx-secondary-color, -20%);
    -fx-border-width: 1.5px;
    /* Agregamos un sutil efecto de sombra para dar apariencia de tarjeta */
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.4), 8, 0, 0, 2);
}

/* Estilo adicional para apariencia de tarjeta */
.card-style {
    -fx-background-color: derive(-fx-primary-color-dark, 15%);
    -fx-padding: 5px 2px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-border-color: derive(-fx-secondary-color, -20%);
    -fx-border-width: 1.5px;
}

/* Estilos específicos para el ListView de bienes y servicios cargados */
#bienServicioCargados {
    /* Configuramos el ListView para que tenga espacio entre celdas */
    -fx-fixed-cell-size: 70.0;
    -fx-cell-horizontal-margin: 5.0;
    -fx-cell-vertical-margin: 10.0;
    -fx-padding: 5px;
    -fx-background-color: derive(-fx-primary-color-dark, -5%);
    -fx-transition: none;
}

/* Aseguramos que las celdas del ListView no tengan efectos de sombra */
#bienServicioCargados .list-cell {
    -fx-effect: none;
    -fx-transition: none;
    /* Aseguramos que no haya cambios de tamaño */
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
    -fx-scale-z: 1.0;
}

/* Estilos para celdas seleccionadas */
#bienServicioCargados .list-cell:selected {
    -fx-background-color: derive(-fx-secondary-color, 10%);
    -fx-effect: none;
    -fx-transition: none;
    /* Aseguramos que no haya cambios de tamaño */
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
    -fx-scale-z: 1.0;
    /* Aseguramos que los labels dentro de las celdas seleccionadas sean visibles */
    -fx-opacity: 1.0;
}

/* Estilos para celdas seleccionadas con hover - mantener el mismo color de selección */
#bienServicioCargados .list-cell:selected:hover {
    -fx-background-color: derive(-fx-secondary-color, 20%);
    /* Podemos agregar un sutil efecto de brillo para indicar el hover sin cambiar el color */
    -fx-effect: innershadow(three-pass-box, derive(-fx-secondary-color-light, 50%), 5, 0, 0, 0);
    -fx-transition: none;
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
    -fx-scale-z: 1.0;
    -fx-opacity: 1.0;
}

/* Estilos para celdas con hover (pero no seleccionadas) */
#bienServicioCargados .list-cell:hover:not(:selected) {
    -fx-background-color: derive(-fx-primary-color-dark, 30%);
    -fx-effect: none;
    -fx-transition: none;
    /* Aseguramos que no haya cambios de tamaño */
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
    -fx-scale-z: 1.0;
}

/* Asegurar que la descripción se ajuste correctamente */
.bien-servicio-descripcion {
    -fx-pref-width: 100%;
    -fx-wrap-text: true;
    -fx-text-overrun: ellipsis;
}

/* Estilos para el código del item */
.bien-servicio-codigo {
    -fx-font-weight: bold;
    -fx-text-fill: -fx-secondary-color;
    -fx-font-size: 15px;
    -fx-transition: none;
    -fx-effect: none;
}

/* Estilo para la etiqueta de marca */
.marca-label {
    -fx-text-fill: -fx-light-color;
    -fx-font-weight: bold;
    -fx-font-size: 12px;
    -fx-transition: none;
    -fx-effect: none;
}

/* Asegurar que los labels dentro de celdas seleccionadas mantengan su visibilidad */
.list-cell:selected .bien-servicio-codigo,
.list-cell:selected .bien-servicio-descripcion,
.list-cell:selected .marca-label,
.list-cell:selected:hover .bien-servicio-codigo,
.list-cell:selected:hover .bien-servicio-descripcion,
.list-cell:selected:hover .marca-label {
    -fx-opacity: 1.0;
    -fx-effect: none;
    -fx-text-fill: -fx-light-color;
}

/* Mantener los colores originales para precio y cantidad cuando están seleccionados */
.list-cell:selected .bien-servicio-precio,
.list-cell:selected:hover .bien-servicio-precio {
    -fx-opacity: 1.0;
    -fx-effect: none;
    -fx-text-fill: -fx-info-color;
}

.list-cell:selected .bien-servicio-cantidad,
.list-cell:selected:hover .bien-servicio-cantidad {
    -fx-opacity: 1.0;
    -fx-effect: none;
    -fx-text-fill: -fx-light-color;
}

.list-cell:selected .bien-servicio-total,
.list-cell:selected:hover .bien-servicio-total {
    -fx-opacity: 1.0;
    -fx-effect: none;
    -fx-text-fill: -fx-success-color;
}

/* Estilos para la descripción del item */
.bien-servicio-descripcion {
    -fx-font-weight: bold;
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 14px;
    -fx-transition: none;
    -fx-effect: none;
}

/* Estilos para el precio y cantidad */
.bien-servicio-precio {
    -fx-font-weight: bold;
    -fx-text-fill: -fx-info-color;
    -fx-font-size: 14px;
    -fx-transition: none;
    -fx-effect: none;
}

.bien-servicio-cantidad {
    -fx-font-weight: bold;
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 18px;
    -fx-transition: none;
    -fx-effect: none;
}

.bien-servicio-total {
    -fx-font-weight: bold;
    -fx-text-fill: -fx-success-color;
    -fx-font-size: 21px;
    -fx-transition: none;
    -fx-effect: none;
}

/* Estilos para items con stock cero o negativo */
.stock-error {
    -fx-background-color: -fx-error-color;
    /* Aseguramos que no haya efectos de sombra */
    -fx-effect: none;
}

.stock-error:hover {
    -fx-background-color: derive(-fx-error-color, 20%);
    /* Aseguramos que no haya efectos de sombra al hacer hover */
    -fx-effect: none;
}

/* ===== Estilos para CustomTextField que actúan como labels editables ===== */
.label-like {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-padding: 0;
    -fx-cursor: default;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-text-box-border: transparent;
    -fx-control-inner-background: transparent;
    -fx-background-radius: 0;
    -fx-border-radius: 0;
}

.label-like:focused {
    -fx-background-color: -fx-control-inner-background;
    -fx-border-color: -fx-accent;
    -fx-border-width: 1;
    -fx-padding: 2;
    -fx-cursor: text;
    -fx-background-radius: 3;
    -fx-border-radius: 3;
}

/* Evitar efectos de hover en CustomTextField para mantener apariencia de label */
.label-like:hover {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-effect: none;
}

/* ===== Estilos para el contenido expandido ===== */
.expanded-content {
    -fx-background-color: derive(-fx-primary-color-dark, 2%);
    -fx-border-color: derive(-fx-secondary-color, -15%);
    -fx-border-width: 1 0 0 0;
    /* Mantener la misma apariencia de card */
    -fx-background-radius: 0 0 5 5;
    /* Altura fija para evitar problemas de layout */
    -fx-pref-height: 50;
    -fx-min-height: 50;
    -fx-max-height: 50;
}

.section-header {
    -fx-text-fill: -fx-secondary-color;
    -fx-font-weight: bold;
}

.section-header-small {
    -fx-text-fill: -fx-secondary-color;
    -fx-font-weight: bold;
}

.detail-label {
    -fx-text-fill: -fx-light-color;
}

.detail-label-small {
    -fx-text-fill: -fx-light-color;
}

/* Estilos específicos para los campos editables en BienServicioCargado */
.bien-servicio-cargado .label-like {
    -fx-font-weight: bold;
    -fx-alignment: center;
    /* Mantener el tamaño original sin expandirse */
    -fx-pref-width: -1;
    -fx-max-width: -1;
}

/* Descripción editable - mantener el espacio original */
.bien-servicio-cargado .bien-servicio-descripcion.label-like {
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 14px;
    -fx-alignment: center-left;
    -fx-padding: 0 3 0 3; /* Mantener padding original */
}

/* Precio acordado editable - mantener tamaño compacto */
.bien-servicio-cargado .bien-servicio-precio.label-like {
    -fx-text-fill: -fx-info-color;
    -fx-font-size: 14px;
    -fx-pref-width: 57; /* Ancho original del precio */
    -fx-max-width: 57;
}

/* Cantidad editable - mantener tamaño compacto */
.bien-servicio-cargado .bien-servicio-cantidad.label-like {
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 18px;
    -fx-pref-width: 41; /* Ancho original de la cantidad */
    -fx-max-width: 41;
}

/* Monto acordado editable - mantener tamaño original */
.bien-servicio-cargado .bien-servicio-total.label-like {
    -fx-text-fill: -fx-success-color;
    -fx-font-size: 21px;
    -fx-pref-width: 90; /* Ancho del panel derecho */
    -fx-max-width: 90;
}
